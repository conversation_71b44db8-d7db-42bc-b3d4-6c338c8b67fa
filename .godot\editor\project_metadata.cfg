[editor_metadata]

executable_path="D:/SteamLibrary/steamapps/common/Godot Engine/godot.windows.opt.tools.64.exe"

[recent_files]

scenes=["res://scenes/Main.tscn", "res://Main.tscn"]
scripts=["res://scripts/MultiplayerLobby.gd", "res://scripts/LevelSelect.gd", "res://MultiplayerTetrisGame.gd", "res://NetworkManager.gd"]

[uid_upgrade_tool]

run_on_restart=false
resave_paths=PackedStringArray()

[export_options]

export_debug=false
export_as_patch=true
default_filename="Tetris"

[dialog_bounds]

export=Rect2(4688, 287, 1800, 1139)
create_new_node=Rect2(5116, 380, 1800, 1400)

[debug_options]

run_debug_collisions=true
run_debug_paths=true
run_debug_navigation=true
run_debug_avoidance=true
