[res://scripts/LevelSelect.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 10,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/MultiplayerLobby.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 57,
"scroll_position": 44.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Player.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 57,
"scroll_position": 57.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/NetworkManager.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 193,
"scroll_position": 180.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/MainMenu.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 38,
"scroll_position": 36.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
