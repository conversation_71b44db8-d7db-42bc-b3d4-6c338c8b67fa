list=[{
"base": &"Node",
"class": &"GameManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/GameManager.gd"
}, {
"base": &"Area2D",
"class": &"Goal",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Goal.gd"
}, {
"base": &"Control",
"class": &"LevelSelect",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/LevelSelect.gd"
}, {
"base": &"Node2D",
"class": &"Main",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Main.gd"
}, {
"base": &"Control",
"class": &"MainMenu",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/MainMenu.gd"
}, {
"base": &"Control",
"class": &"MultiplayerLobby",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/MultiplayerLobby.gd"
}, {
"base": &"CharacterBody2D",
"class": &"Player",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Player.gd"
}, {
"base": &"Control",
"class": &"RoomLobby",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/RoomLobby.gd"
}]
