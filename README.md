# PICO PARK Clone - Godot 多人合作游戏

这是一个使用Godot 4.2制作的PICO PARK风格的合作平台游戏，支持单人和多人局域网游戏。

## 游戏特性

### 🎮 核心游戏机制
- **角色控制**: 流畅的移动、跳跃和攻击系统
- **平台游戏机制**: 包含土狼时间和跳跃缓冲，提供更好的游戏体验
- **攻击系统**: 支持攻击动作，攻击时移动速度减慢
- **关卡系统**: 多个可选关卡，支持不同难度和玩家数限制

### 🌐 多人游戏功能
- **局域网支持**: 自动发现和连接同一局域网内的游戏房间
- **房间系统**: 创建和加入游戏房间，支持最多8名玩家
- **实时同步**: 玩家位置、动作和状态的实时网络同步
- **准备系统**: 玩家准备状态管理，确保所有人都准备好才开始游戏

### 🎯 用户界面
- **主菜单**: 选择单人或多人模式
- **关卡选择**: 浏览和选择可用关卡
- **多人大厅**: 创建房间、搜索房间、设置玩家名称
- **房间管理**: 查看房间内玩家、准备状态、开始游戏

## 控制方式

### 🎮 游戏控制

#### 玩家控制
- **A/D**: 左右移动角色
- **K**: 跳跃
- **J**: 攻击（攻击时移动速度减半）
- **Enter**: 重启关卡（仅主机）
- **ESC**: 返回菜单

#### 界面导航
- **ESC**: 返回上一级菜单
- **Enter**: 确认选择
- **鼠标**: 点击按钮和选项

## 🎯 游戏目标

控制蓝色方块角色到达黄色的目标区域来完成关卡。在多人模式下，玩家需要协作完成更复杂的挑战。

## 🚀 如何开始

### 单人游戏
1. 启动游戏，选择"单人游戏"
2. 在关卡选择界面选择想要挑战的关卡
3. 点击"开始游戏"即可开始

### 多人游戏

#### 创建房间（作为主机）
1. 选择"多人游戏"
2. 输入你的玩家名称
3. 输入房间名称，设置最大玩家数
4. 点击"创建房间"
5. 在房间大厅中选择关卡
6. 等待其他玩家加入并准备
7. 点击"开始游戏"

#### 加入房间
1. 选择"多人游戏"
2. 输入你的玩家名称
3. 在房间列表中找到想要加入的房间
4. 点击"加入"按钮
5. 在房间大厅中点击"准备"
6. 等待主机开始游戏

## 项目结构

```
godot_doc/
├── project.godot          # 项目配置文件
├── icon.svg              # 游戏图标
├── scenes/               # 场景文件
│   ├── Main.tscn        # 主游戏场景
│   ├── Player.tscn      # 玩家角色场景
│   └── Goal.tscn        # 目标物体场景
└── scripts/             # 脚本文件
    ├── Main.gd          # 主游戏逻辑
    ├── Player.gd        # 玩家控制器
    ├── Goal.gd          # 目标物体逻辑
    └── GameManager.gd   # 游戏状态管理
```

## 如何运行

1. 确保已安装Godot 4.2或更高版本
2. 在Godot中打开项目文件夹
3. 点击"播放"按钮或按F5运行游戏

## 游戏机制说明

### 角色物理
- **重力**: 角色受重力影响
- **加速度**: 地面和空中有不同的加速度
- **摩擦力**: 停止移动时会有摩擦减速
- **土狼时间**: 离开平台后短时间内仍可跳跃
- **跳跃缓冲**: 提前按跳跃键会在着地时自动跳跃

### 碰撞层设置
- **Layer 1**: 玩家
- **Layer 2**: 地面
- **Layer 3**: 墙壁  
- **Layer 4**: 平台

## 扩展建议

这个基础版本可以通过以下方式扩展：

1. **多人合作**: 添加多个玩家角色
2. **更多关卡**: 创建更复杂的谜题关卡
3. **互动元素**: 添加开关、门、移动平台等
4. **音效和音乐**: 增加音频反馈
5. **粒子效果**: 添加视觉特效
6. **关卡编辑器**: 允许玩家创建自定义关卡

## 技术特点

- 使用CharacterBody2D实现流畅的角色控制
- 基于状态的动画系统
- 模块化的场景设计
- 清晰的代码结构和注释

享受游戏吧！🎮
