# PICO PARK Clone - Godot 游戏

这是一个使用Godot 4.2制作的PICO PARK风格的合作平台游戏。

## 游戏特性

- **角色控制**: 流畅的移动、跳跃和物理系统
- **平台游戏机制**: 包含土狼时间和跳跃缓冲，提供更好的游戏体验
- **简单关卡**: 包含平台、墙壁和目标点
- **基本动画**: 角色状态动画（待机、跑步、跳跃）
- **游戏管理**: 基本的游戏状态管理和重启功能

## 控制方式

### 玩家控制
- **A/D**: 左右移动角色
- **K**: 跳跃
- **J**: 攻击
- **Enter**: 重启关卡
- **ESC**: 暂停游戏

## 游戏目标

控制蓝色方块角色到达黄色的目标区域来完成关卡。

## 项目结构

```
godot_doc/
├── project.godot          # 项目配置文件
├── icon.svg              # 游戏图标
├── scenes/               # 场景文件
│   ├── Main.tscn        # 主游戏场景
│   ├── Player.tscn      # 玩家角色场景
│   └── Goal.tscn        # 目标物体场景
└── scripts/             # 脚本文件
    ├── Main.gd          # 主游戏逻辑
    ├── Player.gd        # 玩家控制器
    ├── Goal.gd          # 目标物体逻辑
    └── GameManager.gd   # 游戏状态管理
```

## 如何运行

1. 确保已安装Godot 4.2或更高版本
2. 在Godot中打开项目文件夹
3. 点击"播放"按钮或按F5运行游戏

## 游戏机制说明

### 角色物理
- **重力**: 角色受重力影响
- **加速度**: 地面和空中有不同的加速度
- **摩擦力**: 停止移动时会有摩擦减速
- **土狼时间**: 离开平台后短时间内仍可跳跃
- **跳跃缓冲**: 提前按跳跃键会在着地时自动跳跃

### 碰撞层设置
- **Layer 1**: 玩家
- **Layer 2**: 地面
- **Layer 3**: 墙壁  
- **Layer 4**: 平台

## 扩展建议

这个基础版本可以通过以下方式扩展：

1. **多人合作**: 添加多个玩家角色
2. **更多关卡**: 创建更复杂的谜题关卡
3. **互动元素**: 添加开关、门、移动平台等
4. **音效和音乐**: 增加音频反馈
5. **粒子效果**: 添加视觉特效
6. **关卡编辑器**: 允许玩家创建自定义关卡

## 技术特点

- 使用CharacterBody2D实现流畅的角色控制
- 基于状态的动画系统
- 模块化的场景设计
- 清晰的代码结构和注释

享受游戏吧！🎮
