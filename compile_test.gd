extends Node

func _ready():
	print("Testing basic compilation...")
	
	# Test GameData access
	if GameData:
		print("✓ GameData accessible")
		print("  Game mode: ", GameData.game_mode)
		print("  Available levels: ", GameData.available_levels.size())
	else:
		print("✗ GameData not accessible")
	
	# Test NetworkManager access
	if NetworkManager:
		print("✓ NetworkManager accessible")
		print("  Is hosting: ", NetworkManager.is_hosting)
	else:
		print("✗ NetworkManager not accessible")
	
	print("Basic compilation test complete!")
	
	# Don't quit automatically, let user see results
