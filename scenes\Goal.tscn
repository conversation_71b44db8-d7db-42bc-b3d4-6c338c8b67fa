[gd_scene load_steps=6 format=3 uid="uid://b7q568xclyp0u"]

[ext_resource type="Script" uid="uid://h38i00383qce" path="res://scripts/Goal.gd" id="1_goal"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_goal"]
size = Vector2(48, 48)

[sub_resource type="Animation" id="Animation_idle"]
resource_name = "idle"
length = 2.0
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite:color")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 1, 2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 0, 1), Color(1, 0.8, 0, 1), Color(1, 1, 0, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Sprite:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 1, 2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.1, 1.1), Vector2(1, 1)]
}

[sub_resource type="Animation" id="Animation_reached"]
resource_name = "reached"
length = 0.5
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite:color")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.25, 0.5),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 0, 1), Color(0, 1, 0, 1), Color(0, 1, 0, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Sprite:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.25, 0.5),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.5, 1.5), Vector2(1.2, 1.2)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_goal"]
_data = {
&"idle": SubResource("Animation_idle"),
&"reached": SubResource("Animation_reached")
}

[node name="Goal" type="Area2D"]
collision_layer = 0
collision_mask = 1
script = ExtResource("1_goal")

[node name="Sprite" type="ColorRect" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -24.0
offset_top = -24.0
offset_right = 24.0
offset_bottom = 24.0
color = Color(1, 1, 0, 1)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_goal")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("AnimationLibrary_goal")
}
