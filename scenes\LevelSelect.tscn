[gd_scene load_steps=2 format=3 uid="uid://level_select_uid"]

[ext_resource type="Script" path="res://scripts/LevelSelect.gd" id="1_levelselect"]

[node name="LevelSelect" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_levelselect")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.1, 0.1, 0.2, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 50.0
offset_top = 50.0
offset_right = -50.0
offset_bottom = -50.0

[node name="TopBar" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 60)

[node name="BackButton" type="Button" parent="VBoxContainer/TopBar"]
layout_mode = 2
custom_minimum_size = Vector2(100, 0)
text = "返回"

[node name="TitleLabel" type="Label" parent="VBoxContainer/TopBar"]
layout_mode = 2
size_flags_horizontal = 3
text = "选择关卡"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="MainContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ScrollContainer" type="ScrollContainer" parent="VBoxContainer/MainContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="LevelList" type="VBoxContainer" parent="VBoxContainer/MainContainer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="VSeparator" type="VSeparator" parent="VBoxContainer/MainContainer"]
layout_mode = 2

[node name="LevelInfoPanel" type="Panel" parent="VBoxContainer/MainContainer"]
layout_mode = 2
custom_minimum_size = Vector2(400, 0)
visible = false

[node name="VBoxContainer" type="VBoxContainer" parent="VBoxContainer/MainContainer/LevelInfoPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0

[node name="LevelNameLabel" type="Label" parent="VBoxContainer/MainContainer/LevelInfoPanel/VBoxContainer"]
layout_mode = 2
text = "关卡名称"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer/MainContainer/LevelInfoPanel/VBoxContainer"]
layout_mode = 2

[node name="LevelDescriptionLabel" type="Label" parent="VBoxContainer/MainContainer/LevelInfoPanel/VBoxContainer"]
layout_mode = 2
text = "关卡描述"
autowrap_mode = 3

[node name="MaxPlayersLabel" type="Label" parent="VBoxContainer/MainContainer/LevelInfoPanel/VBoxContainer"]
layout_mode = 2
text = "最大玩家数: 4"

[node name="Spacer" type="Control" parent="VBoxContainer/MainContainer/LevelInfoPanel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="StartButton" type="Button" parent="VBoxContainer/MainContainer/LevelInfoPanel/VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)
text = "开始游戏"
