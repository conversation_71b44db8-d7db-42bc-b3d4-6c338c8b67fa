[gd_scene load_steps=7 format=3 uid="uid://c063ru5iwp4q3"]

[ext_resource type="Script" uid="uid://bsopm2gxmphxo" path="res://scripts/Main.gd" id="1_main"]
[ext_resource type="PackedScene" uid="uid://b7q568xclyp0u" path="res://scenes/Goal.tscn" id="2_goal"]
[ext_resource type="Script" uid="uid://c8gcu2jguv05p" path="res://scripts/GameManager.gd" id="3_gamemanager"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_ground"]
size = Vector2(1280, 64)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_platform"]
size = Vector2(160, 32)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_wall"]
size = Vector2(64, 720)

[node name="Main" type="Node2D"]
script = ExtResource("1_main")

[node name="GameManager" type="Node" parent="."]
script = ExtResource("3_gamemanager")

[node name="PlayersContainer" type="Node2D" parent="."]

[node name="LevelContainer" type="Node2D" parent="."]

[node name="Ground" type="StaticBody2D" parent="LevelContainer"]
position = Vector2(640, 680)
collision_layer = 2
collision_mask = 0

[node name="GroundSprite" type="ColorRect" parent="LevelContainer/Ground"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -640.0
offset_top = -32.0
offset_right = 640.0
offset_bottom = 32.0
color = Color(0.4, 0.3, 0.2, 1)

[node name="GroundCollision" type="CollisionShape2D" parent="LevelContainer/Ground"]
shape = SubResource("RectangleShape2D_ground")

[node name="Platform1" type="StaticBody2D" parent="LevelContainer"]
position = Vector2(300, 500)
collision_layer = 8
collision_mask = 0

[node name="PlatformSprite" type="ColorRect" parent="LevelContainer/Platform1"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -80.0
offset_top = -16.0
offset_right = 80.0
offset_bottom = 16.0
color = Color(0.6, 0.4, 0.2, 1)

[node name="PlatformCollision" type="CollisionShape2D" parent="LevelContainer/Platform1"]
shape = SubResource("RectangleShape2D_platform")

[node name="Platform2" type="StaticBody2D" parent="LevelContainer"]
position = Vector2(600, 400)
collision_layer = 8
collision_mask = 0

[node name="PlatformSprite" type="ColorRect" parent="LevelContainer/Platform2"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -80.0
offset_top = -16.0
offset_right = 80.0
offset_bottom = 16.0
color = Color(0.6, 0.4, 0.2, 1)

[node name="PlatformCollision" type="CollisionShape2D" parent="LevelContainer/Platform2"]
shape = SubResource("RectangleShape2D_platform")

[node name="Platform3" type="StaticBody2D" parent="LevelContainer"]
position = Vector2(900, 300)
collision_layer = 8
collision_mask = 0

[node name="PlatformSprite" type="ColorRect" parent="LevelContainer/Platform3"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -80.0
offset_top = -16.0
offset_right = 80.0
offset_bottom = 16.0
color = Color(0.6, 0.4, 0.2, 1)

[node name="PlatformCollision" type="CollisionShape2D" parent="LevelContainer/Platform3"]
shape = SubResource("RectangleShape2D_platform")

[node name="LeftWall" type="StaticBody2D" parent="LevelContainer"]
position = Vector2(-32, 360)
collision_layer = 4
collision_mask = 0

[node name="WallSprite" type="ColorRect" parent="LevelContainer/LeftWall"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -32.0
offset_top = -360.0
offset_right = 32.0
offset_bottom = 360.0
color = Color(0.3, 0.3, 0.3, 1)

[node name="WallCollision" type="CollisionShape2D" parent="LevelContainer/LeftWall"]
shape = SubResource("RectangleShape2D_wall")

[node name="RightWall" type="StaticBody2D" parent="LevelContainer"]
position = Vector2(1312, 360)
collision_layer = 4
collision_mask = 0

[node name="WallSprite" type="ColorRect" parent="LevelContainer/RightWall"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -32.0
offset_top = -360.0
offset_right = 32.0
offset_bottom = 360.0
color = Color(0.3, 0.3, 0.3, 1)

[node name="WallCollision" type="CollisionShape2D" parent="LevelContainer/RightWall"]
shape = SubResource("RectangleShape2D_wall")

[node name="Goal" parent="LevelContainer" instance=ExtResource("2_goal")]
position = Vector2(1100, 250)

[node name="UIContainer" type="CanvasLayer" parent="."]

[node name="UI" type="Control" parent="UIContainer"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Instructions" type="Label" parent="UIContainer/UI"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -100.0
offset_right = 400.0
offset_bottom = -20.0
grow_vertical = 0
text = "WASD/Arrow Keys: Move
Space/W/Up: Jump
Enter: Restart Level"
vertical_alignment = 2

[node name="Camera2D" type="Camera2D" parent="."]
position = Vector2(640, 360)
