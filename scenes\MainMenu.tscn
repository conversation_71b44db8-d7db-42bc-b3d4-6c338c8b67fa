[gd_scene load_steps=3 format=3 uid="uid://main_menu_uid"]

[ext_resource type="Script" path="res://scripts/MainMenu.gd" id="1_mainmenu"]

[sub_resource type="LabelSettings" id="LabelSettings_1"]
font_size = 48
font_color = Color(1, 1, 1, 1)

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_mainmenu")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.1, 0.1, 0.2, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -150.0
offset_right = 200.0
offset_bottom = 150.0

[node name="TitleLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "PICO PARK CLONE"
label_settings = SubResource("LabelSettings_1")
horizontal_alignment = 1

[node name="Spacer1" type="Control" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)

[node name="ButtonContainer" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(300, 0)

[node name="SinglePlayerButton" type="Button" parent="VBoxContainer/ButtonContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)
text = "单人游戏"

[node name="MultiplayerButton" type="Button" parent="VBoxContainer/ButtonContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)
text = "多人游戏"

[node name="SettingsButton" type="Button" parent="VBoxContainer/ButtonContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)
text = "设置"

[node name="QuitButton" type="Button" parent="VBoxContainer/ButtonContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)
text = "退出游戏"

[node name="VersionLabel" type="Label" parent="."]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -100.0
offset_top = -30.0
text = "v1.0.0"
horizontal_alignment = 2
