[gd_scene load_steps=2 format=3 uid="uid://multiplayer_lobby_uid"]

[ext_resource type="Script" path="res://scripts/MultiplayerLobby.gd" id="1_multiplayerlobby"]

[node name="MultiplayerLobby" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_multiplayerlobby")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.1, 0.1, 0.2, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 50.0
offset_top = 50.0
offset_right = -50.0
offset_bottom = -50.0

[node name="TopBar" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 60)

[node name="BackButton" type="Button" parent="VBoxContainer/TopBar"]
layout_mode = 2
custom_minimum_size = Vector2(100, 0)
text = "返回"

[node name="TitleLabel" type="Label" parent="VBoxContainer/TopBar"]
layout_mode = 2
size_flags_horizontal = 3
text = "多人游戏大厅"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="PlayerInfo" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="VBoxContainer/PlayerInfo"]
layout_mode = 2
text = "玩家信息"

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer/PlayerInfo"]
layout_mode = 2

[node name="Label" type="Label" parent="VBoxContainer/PlayerInfo/HBoxContainer"]
layout_mode = 2
text = "玩家名称:"
custom_minimum_size = Vector2(100, 0)

[node name="PlayerNameEdit" type="LineEdit" parent="VBoxContainer/PlayerInfo/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
placeholder_text = "输入你的名称"

[node name="HSeparator2" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="CreateRoomSection" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="VBoxContainer/CreateRoomSection"]
layout_mode = 2
text = "创建房间"

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer/CreateRoomSection"]
layout_mode = 2

[node name="Label" type="Label" parent="VBoxContainer/CreateRoomSection/HBoxContainer"]
layout_mode = 2
text = "房间名称:"
custom_minimum_size = Vector2(100, 0)

[node name="RoomNameEdit" type="LineEdit" parent="VBoxContainer/CreateRoomSection/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
placeholder_text = "输入房间名称"

[node name="Label2" type="Label" parent="VBoxContainer/CreateRoomSection/HBoxContainer"]
layout_mode = 2
text = "最大玩家:"
custom_minimum_size = Vector2(100, 0)

[node name="MaxPlayersSpinBox" type="SpinBox" parent="VBoxContainer/CreateRoomSection/HBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(80, 0)

[node name="CreateRoomButton" type="Button" parent="VBoxContainer/CreateRoomSection"]
layout_mode = 2
custom_minimum_size = Vector2(0, 40)
text = "创建房间"

[node name="HSeparator3" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="RoomListSection" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="TopBar" type="HBoxContainer" parent="VBoxContainer/RoomListSection"]
layout_mode = 2

[node name="Label" type="Label" parent="VBoxContainer/RoomListSection/TopBar"]
layout_mode = 2
size_flags_horizontal = 3
text = "可用房间"

[node name="RefreshButton" type="Button" parent="VBoxContainer/RoomListSection/TopBar"]
layout_mode = 2
text = "刷新"

[node name="ScrollContainer" type="ScrollContainer" parent="VBoxContainer/RoomListSection"]
layout_mode = 2
size_flags_vertical = 3

[node name="RoomList" type="VBoxContainer" parent="VBoxContainer/RoomListSection/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="StatusLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "状态: 准备就绪"
horizontal_alignment = 1
