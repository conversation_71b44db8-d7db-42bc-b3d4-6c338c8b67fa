[gd_scene load_steps=7 format=3 uid="uid://bvxvqkqxqxqxq"]

[ext_resource type="Script" uid="uid://bnst0qgvne7h6" path="res://scripts/Player.gd" id="1_1a2b3"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(32, 48)

[sub_resource type="Animation" id="Animation_idle"]
resource_name = "idle"
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.5, 1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(0.9, 0.9, 0.9, 1), Color(1, 1, 1, 1)]
}

[sub_resource type="Animation" id="Animation_jump"]
resource_name = "jump"
length = 0.3
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.15, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(0.8, 1.2), Vector2(1, 1)]
}

[sub_resource type="Animation" id="Animation_run"]
resource_name = "run"
length = 0.6
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.15, 0.3, 0.45),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.1, 0.9), Vector2(1, 1), Vector2(1.1, 0.9)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1"]
_data = {
&"idle": SubResource("Animation_idle"),
&"jump": SubResource("Animation_jump"),
&"run": SubResource("Animation_run")
}

[node name="Player" type="CharacterBody2D"]
collision_layer = 1
collision_mask = 14
script = ExtResource("1_1a2b3")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(0.5, 0.8, 1, 1)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_1")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("AnimationLibrary_1")
}
