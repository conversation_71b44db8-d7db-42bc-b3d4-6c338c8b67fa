[gd_scene load_steps=2 format=3 uid="uid://room_lobby_uid"]

[ext_resource type="Script" path="res://scripts/RoomLobby.gd" id="1_roomlobby"]

[node name="RoomLobby" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_roomlobby")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.1, 0.1, 0.2, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 50.0
offset_top = 50.0
offset_right = -50.0
offset_bottom = -50.0

[node name="TopBar" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 60)

[node name="BackButton" type="Button" parent="VBoxContainer/TopBar"]
layout_mode = 2
custom_minimum_size = Vector2(100, 0)
text = "离开房间"

[node name="RoomNameLabel" type="Label" parent="VBoxContainer/TopBar"]
layout_mode = 2
size_flags_horizontal = 3
text = "房间名称"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="LevelInfo" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)

[node name="LevelNameLabel" type="Label" parent="VBoxContainer/LevelInfo"]
layout_mode = 2
size_flags_horizontal = 3
text = "当前关卡: 未选择"
vertical_alignment = 1

[node name="ChangeLevelButton" type="Button" parent="VBoxContainer/LevelInfo"]
layout_mode = 2
text = "更改关卡"

[node name="HSeparator2" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="PlayerSection" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="Label" type="Label" parent="VBoxContainer/PlayerSection"]
layout_mode = 2
text = "玩家列表"

[node name="ScrollContainer" type="ScrollContainer" parent="VBoxContainer/PlayerSection"]
layout_mode = 2
size_flags_vertical = 3

[node name="PlayerList" type="VBoxContainer" parent="VBoxContainer/PlayerSection/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="HSeparator3" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="BottomBar" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 60)

[node name="ReadyButton" type="Button" parent="VBoxContainer/BottomBar"]
layout_mode = 2
size_flags_horizontal = 3
text = "准备"

[node name="StartGameButton" type="Button" parent="VBoxContainer/BottomBar"]
layout_mode = 2
size_flags_horizontal = 3
text = "开始游戏"

[node name="StatusLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "状态: 等待玩家"
horizontal_alignment = 1
