extends Node

# 游戏模式枚举
enum GameMode {
	SINGLE_PLAYER,
	MULTIPLAYER
}

# 关卡信息结构
class LevelInfo:
	var id: String
	var name: String
	var description: String
	var scene_path: String
	var preview_image: String
	var max_players: int
	
	func _init(p_id: String, p_name: String, p_description: String, p_scene_path: String, p_max_players: int = 4):
		id = p_id
		name = p_name
		description = p_description
		scene_path = p_scene_path
		max_players = p_max_players

# 全局游戏状态
var game_mode: GameMode = GameMode.SINGLE_PLAYER
var selected_level
var player_name: String = "Player"
var max_players: int = 4

# 可用关卡列表
var available_levels: Array = []

# 网络相关
var is_host: bool = false
var room_name: String = ""
var connected_players: Array = []

func _ready():
	# 初始化关卡数据
	setup_levels()
	
	# 设置默认玩家名称
	player_name = "Player_" + str(randi() % 1000)

func setup_levels():
	# 添加可用关卡
	available_levels.clear()
	
	available_levels.append(LevelInfo.new(
		"level_01",
		"基础关卡",
		"学习基本的移动和跳跃",
		"res://scenes/Main.tscn",
		4
	))
	
	available_levels.append(LevelInfo.new(
		"level_02", 
		"合作挑战",
		"需要玩家协作完成的关卡",
		"res://scenes/levels/Level02.tscn",
		4
	))
	
	available_levels.append(LevelInfo.new(
		"level_03",
		"高难度关卡", 
		"考验操作技巧的困难关卡",
		"res://scenes/levels/Level03.tscn",
		2
	))
	
	# 设置默认选中第一个关卡
	if available_levels.size() > 0:
		selected_level = available_levels[0]

func get_level_by_id(level_id: String):
	for level in available_levels:
		if level.id == level_id:
			return level
	return null

func is_multiplayer() -> bool:
	return game_mode == GameMode.MULTIPLAYER

func reset_multiplayer_data():
	is_host = false
	room_name = ""
	connected_players.clear()

# 保存/加载玩家设置
func save_settings():
	var config = ConfigFile.new()
	config.set_value("player", "name", player_name)
	config.save("user://settings.cfg")

func load_settings():
	var config = ConfigFile.new()
	if config.load("user://settings.cfg") == OK:
		player_name = config.get_value("player", "name", "Player_" + str(randi() % 1000))
