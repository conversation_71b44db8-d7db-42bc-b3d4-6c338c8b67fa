extends Node
class_name GameManager

# 游戏状态枚举
enum GameState {
	MENU,
	PLAYING,
	PAUSED,
	GAME_OVER
}

# 当前游戏状态
var current_state: GameState = GameState.PLAYING

# 信号
signal state_changed(new_state: GameState)
signal player_died(player: Player)
signal level_completed()

# 单例实例
static var instance: GameManager

func _ready():
	# 设置单例
	if instance == null:
		instance = self
	else:
		queue_free()
		return
	
	# 连接信号
	process_mode = Node.PROCESS_MODE_ALWAYS

func _input(event):
	# 处理全局输入
	if event.is_action_pressed("ui_cancel"):  # ESC键
		toggle_pause()

func change_state(new_state: GameState):
	if current_state != new_state:
		current_state = new_state
		state_changed.emit(new_state)
		
		match new_state:
			GameState.PAUSED:
				get_tree().paused = true
			GameState.PLAYING:
				get_tree().paused = false
			GameState.GAME_OVER:
				handle_game_over()

func toggle_pause():
	if current_state == GameState.PLAYING:
		change_state(GameState.PAUSED)
	elif current_state == GameState.PAUSED:
		change_state(GameState.PLAYING)

func handle_game_over():
	print("Game Over!")
	# 这里可以添加游戏结束的逻辑

func restart_game():
	get_tree().reload_current_scene()

# 静态方法来获取实例
static func get_instance() -> GameManager:
	return instance
