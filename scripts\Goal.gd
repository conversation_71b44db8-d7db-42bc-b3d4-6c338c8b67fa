extends Area2D
class_name Goal

# 信号
signal goal_reached(player)

# 动画相关
@onready var sprite: ColorRect = $Sprite
@onready var animation_player: AnimationPlayer = $AnimationPlayer

var is_activated: bool = false

func _ready():
	# 连接信号
	body_entered.connect(_on_body_entered)
	
	# 开始动画
	if animation_player:
		animation_player.play("idle")

func _on_body_entered(body):
	if body is Player and not is_activated:
		is_activated = true
		goal_reached.emit(body)
		
		# 播放到达动画
		if animation_player:
			animation_player.play("reached")
		
		print("Goal reached by player!")
		
		# 可以在这里添加粒子效果、音效等
