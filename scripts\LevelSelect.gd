extends Control
class_name LevelSelect

# 节点引用
@onready var back_button: Button = $VBoxContainer/TopBar/BackButton
@onready var title_label: Label = $VBoxContainer/TopBar/TitleLabel
@onready var level_list: VBoxContainer = $VBoxContainer/ScrollContainer/LevelList
@onready var level_info_panel: Panel = $VBoxContainer/LevelInfoPanel
@onready var level_name_label: Label = $VBoxContainer/LevelInfoPanel/VBoxContainer/LevelNameLabel
@onready var level_description_label: Label = $VBoxContainer/LevelInfoPanel/VBoxContainer/LevelDescriptionLabel
@onready var max_players_label: Label = $VBoxContainer/LevelInfoPanel/VBoxContainer/MaxPlayersLabel
@onready var start_button: Button = $VBoxContainer/LevelInfoPanel/VBoxContainer/StartButton

# 当前选中的关卡
var selected_level

func _ready():
	# 连接信号
	back_button.pressed.connect(_on_back_pressed)
	start_button.pressed.connect(_on_start_pressed)
	
	# 设置标题
	if GameData.is_multiplayer():
		title_label.text = "选择关卡 - 多人模式"
	else:
		title_label.text = "选择关卡 - 单人模式"
	
	# 加载关卡列表
	load_levels()
	
	# 选择默认关卡
	if GameData.selected_level:
		select_level(GameData.selected_level)

func load_levels():
	# 清空现有按钮
	for child in level_list.get_children():
		child.queue_free()
	
	# 为每个关卡创建按钮
	for level in GameData.available_levels:
		var level_button = create_level_button(level)
		level_list.add_child(level_button)

func create_level_button(level) -> Button:
	var button = Button.new()
	button.text = level.name
	button.custom_minimum_size = Vector2(400, 60)
	button.alignment = HORIZONTAL_ALIGNMENT_LEFT
	
	# 设置按钮样式
	var style_normal = StyleBoxFlat.new()
	style_normal.bg_color = Color(0.2, 0.2, 0.3, 1)
	style_normal.border_width_left = 2
	style_normal.border_width_right = 2
	style_normal.border_width_top = 2
	style_normal.border_width_bottom = 2
	style_normal.border_color = Color(0.4, 0.4, 0.5, 1)
	button.add_theme_stylebox_override("normal", style_normal)
	
	var style_hover = StyleBoxFlat.new()
	style_hover.bg_color = Color(0.3, 0.3, 0.4, 1)
	style_hover.border_width_left = 2
	style_hover.border_width_right = 2
	style_hover.border_width_top = 2
	style_hover.border_width_bottom = 2
	style_hover.border_color = Color(0.6, 0.6, 0.7, 1)
	button.add_theme_stylebox_override("hover", style_hover)
	
	# 连接按钮信号
	button.pressed.connect(func(): select_level(level))
	
	return button

func select_level(level):
	selected_level = level
	GameData.selected_level = level
	
	# 更新信息面板
	level_name_label.text = level.name
	level_description_label.text = level.description
	max_players_label.text = "最大玩家数: " + str(level.max_players)
	
	# 显示信息面板
	level_info_panel.visible = true
	
	# 检查多人模式下的玩家数限制
	if GameData.is_multiplayer() and GameData.max_players > level.max_players:
		start_button.disabled = true
		start_button.text = "玩家数超出限制"
	else:
		start_button.disabled = false
		start_button.text = "开始游戏"

func _on_back_pressed():
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func _on_start_pressed():
	if not selected_level:
		return
	
	if GameData.is_multiplayer():
		# 多人模式：返回到多人大厅，带着选中的关卡信息
		get_tree().change_scene_to_file("res://scenes/MultiplayerLobby.tscn")
	else:
		# 单人模式：直接开始游戏
		get_tree().change_scene_to_file(selected_level.scene_path)

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
