extends Node2D
class_name Main

# 玩家场景预制体
@export var player_scene: PackedScene = preload("res://scenes/Player.tscn")

# 玩家生成点
@export var spawn_points: Array[Vector2] = []

# 玩家实例
var players: Array[Player] = []

# 节点引用
@onready var players_container: Node2D = $PlayersContainer
@onready var level_container: Node2D = $LevelContainer
@onready var ui_container: CanvasLayer = $UIContainer
@onready var goal: Goal = $LevelContainer/Goal
@onready var game_manager: GameManager = $GameManager

func _ready():
	setup_level()
	spawn_players()
	connect_signals()

func connect_signals():
	# 连接目标信号
	if goal:
		goal.goal_reached.connect(_on_goal_reached)

func setup_level():
	# 设置默认的生成点
	if spawn_points.is_empty():
		spawn_points = [Vector2(100, 400)]

	print("Level setup complete")

func spawn_players():
	# 生成玩家（目前只生成一个玩家）
	for i in range(1):  # 可以修改这个数字来支持多玩家
		var player = player_scene.instantiate() as Player
		if player:
			players_container.add_child(player)
			
			# 设置生成位置
			if i < spawn_points.size():
				player.global_position = spawn_points[i]
			else:
				player.global_position = spawn_points[0]
			
			players.append(player)
			print("Player ", i + 1, " spawned at ", player.global_position)

func restart_level():
	# 重置所有玩家到生成点
	for i in range(players.size()):
		if players[i] and i < spawn_points.size():
			players[i].reset_position(spawn_points[i])

func _input(event):
	# 按R键重启关卡
	if event.is_action_pressed("ui_accept"):  # Enter键
		restart_level()
		print("Level restarted")

# 添加新玩家的函数（为多人游戏准备）
func add_player(spawn_index: int = 0):
	var player = player_scene.instantiate() as Player
	if player:
		players_container.add_child(player)
		
		if spawn_index < spawn_points.size():
			player.global_position = spawn_points[spawn_index]
		else:
			player.global_position = spawn_points[0]
		
		players.append(player)
		return player
	return null

# 移除玩家
func remove_player(player: Player):
	if player in players:
		players.erase(player)
		player.queue_free()

# 目标到达处理
func _on_goal_reached(player: Player):
	print("Level completed by ", player.name)
	# 这里可以添加关卡完成的逻辑
	# 比如显示完成界面、加载下一关等
	await get_tree().create_timer(2.0).timeout
	restart_level()
