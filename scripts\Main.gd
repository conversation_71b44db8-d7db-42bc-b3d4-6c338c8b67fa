extends Node2D
class_name Main

# 玩家场景预制体
@export var player_scene: PackedScene = preload("res://scenes/Player.tscn")

# 玩家生成点
@export var spawn_points: Array[Vector2] = []

# 玩家实例
var players: Array[Player] = []
var network_players: Dictionary = {}  # peer_id -> Player

# 节点引用
@onready var players_container: Node2D = $PlayersContainer
@onready var level_container: Node2D = $LevelContainer
@onready var ui_container: CanvasLayer = $UIContainer
@onready var goal: Goal = $LevelContainer/Goal
@onready var game_manager: GameManager = $GameManager

func _ready():
	setup_level()

	# 连接网络信号
	if GameData.is_multiplayer():
		setup_multiplayer()
	else:
		spawn_single_player()

	connect_signals()

func connect_signals():
	# 连接目标信号
	if goal:
		goal.goal_reached.connect(_on_goal_reached)

func setup_level():
	# 设置默认的生成点
	if spawn_points.is_empty():
		spawn_points = [Vector2(100, 400)]

	print("Level setup complete")

func setup_multiplayer():
	# 连接多人游戏信号
	multiplayer.peer_connected.connect(_on_peer_connected)
	multiplayer.peer_disconnected.connect(_on_peer_disconnected)

	# 为每个连接的玩家生成角色
	for peer_id in NetworkManager.connected_players:
		spawn_player_for_peer(peer_id)

func spawn_single_player():
	# 单人模式：只生成一个玩家
	var player = spawn_player_for_peer(1)
	if player:
		# 设置玩家为可控制
		player.set_multiplayer_authority(1)

func spawn_player_for_peer(peer_id: int) -> Player:
	var player = player_scene.instantiate() as Player
	if not player:
		return null

	# 设置玩家网络权限
	player.set_multiplayer_authority(peer_id)
	player.name = "Player_" + str(peer_id)

	# 添加到场景
	players_container.add_child(player)

	# 设置生成位置
	var spawn_index = network_players.size() % spawn_points.size()
	if spawn_index < spawn_points.size():
		player.global_position = spawn_points[spawn_index]
	else:
		player.global_position = spawn_points[0]

	# 记录玩家
	players.append(player)
	network_players[peer_id] = player

	print("Player spawned for peer ", peer_id, " at ", player.global_position)
	return player

func restart_level():
	# 重置所有玩家到生成点
	var index = 0
	for player in players:
		if player and index < spawn_points.size():
			player.reset_position(spawn_points[index])
			index += 1

func _input(event):
	# 按R键重启关卡
	if event.is_action_pressed("ui_accept"):  # Enter键
		if GameData.is_multiplayer() and not NetworkManager.is_hosting:
			return  # 只有主机可以重启关卡
		restart_level()
		print("Level restarted")

	# ESC键返回菜单
	if event.is_action_pressed("ui_cancel"):
		return_to_menu()

# 添加新玩家的函数（为多人游戏准备）
func add_player(spawn_index: int = 0):
	var player = player_scene.instantiate() as Player
	if player:
		players_container.add_child(player)
		
		if spawn_index < spawn_points.size():
			player.global_position = spawn_points[spawn_index]
		else:
			player.global_position = spawn_points[0]
		
		players.append(player)
		return player
	return null

# 移除玩家
func remove_player(player: Player):
	if player in players:
		players.erase(player)
		player.queue_free()

# 目标到达处理
func _on_goal_reached(player: Player):
	print("Level completed by ", player.name)
	# 这里可以添加关卡完成的逻辑
	# 比如显示完成界面、加载下一关等
	await get_tree().create_timer(2.0).timeout
	restart_level()

# 网络事件处理
func _on_peer_connected(peer_id: int):
	print("Peer connected: ", peer_id)
	# 为新连接的玩家生成角色
	spawn_player_for_peer(peer_id)

func _on_peer_disconnected(peer_id: int):
	print("Peer disconnected: ", peer_id)
	# 移除断开连接的玩家
	if peer_id in network_players:
		var player = network_players[peer_id]
		if player:
			players.erase(player)
			player.queue_free()
		network_players.erase(peer_id)

# 返回菜单
func return_to_menu():
	if GameData.is_multiplayer():
		# 多人模式：断开连接并返回多人大厅
		NetworkManager.disconnect_from_room()
		get_tree().change_scene_to_file("res://scenes/MultiplayerLobby.tscn")
	else:
		# 单人模式：返回主菜单
		get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")
