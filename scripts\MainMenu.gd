extends Control
class_name MainMenu

# 节点引用
@onready var title_label: Label = $VBoxContainer/TitleLabel
@onready var single_player_button: Button = $VBoxContainer/ButtonContainer/SinglePlayerButton
@onready var multiplayer_button: Button = $VBoxContainer/ButtonContainer/MultiplayerButton
@onready var settings_button: Button = $VBoxContainer/ButtonContainer/SettingsButton
@onready var quit_button: Button = $VBoxContainer/ButtonContainer/QuitButton

# 场景路径
const LEVEL_SELECT_SCENE = "res://scenes/LevelSelect.tscn"
const MULTIPLAYER_LOBBY_SCENE = "res://scenes/MultiplayerLobby.tscn"

func _ready():
	# 连接按钮信号
	single_player_button.pressed.connect(_on_single_player_pressed)
	multiplayer_button.pressed.connect(_on_multiplayer_pressed)
	settings_button.pressed.connect(_on_settings_pressed)
	quit_button.pressed.connect(_on_quit_pressed)
	
	# 设置按钮焦点
	single_player_button.grab_focus()
	
	print("Main Menu loaded")

func _on_single_player_pressed():
	print("Single Player selected")
	# 设置游戏模式为单人
	GameData.game_mode = GameData.GameMode.SINGLE_PLAYER
	# 跳转到关卡选择
	get_tree().change_scene_to_file(LEVEL_SELECT_SCENE)

func _on_multiplayer_pressed():
	print("Multiplayer selected")
	# 设置游戏模式为多人
	GameData.game_mode = GameData.GameMode.MULTIPLAYER
	# 跳转到多人大厅
	get_tree().change_scene_to_file(MULTIPLAYER_LOBBY_SCENE)

func _on_settings_pressed():
	print("Settings pressed")
	# 这里可以添加设置界面
	# 暂时显示一个简单的对话框
	show_message("设置功能即将推出！")

func _on_quit_pressed():
	print("Quit pressed")
	get_tree().quit()

func show_message(text: String):
	# 创建一个简单的消息对话框
	var dialog = AcceptDialog.new()
	dialog.dialog_text = text
	add_child(dialog)
	dialog.popup_centered()
	dialog.confirmed.connect(func(): dialog.queue_free())

func _input(event):
	# 处理ESC键退出
	if event.is_action_pressed("ui_cancel"):
		_on_quit_pressed()
