extends Control
class_name MultiplayerLobby

# 节点引用
@onready var back_button: Button = $VBoxContainer/TopBar/BackButton
@onready var player_name_edit: LineEdit = $VBoxContainer/PlayerInfo/HBoxContainer/PlayerNameEdit
@onready var create_room_button: Button = $VBoxContainer/CreateRoomSection/CreateRoomButton
@onready var room_name_edit: LineEdit = $VBoxContainer/CreateRoomSection/HBoxContainer/RoomNameEdit
@onready var max_players_spin: SpinBox = $VBoxContainer/CreateRoomSection/HBoxContainer/MaxPlayersSpinBox
@onready var refresh_button: Button = $VBoxContainer/RoomListSection/TopBar/RefreshButton
@onready var room_list: VBoxContainer = $VBoxContainer/RoomListSection/ScrollContainer/RoomList
@onready var status_label: Label = $VBoxContainer/StatusLabel

func _ready():
	# 网络管理器是自动加载的，直接使用
	
	# 连接信号
	back_button.pressed.connect(_on_back_pressed)
	create_room_button.pressed.connect(_on_create_room_pressed)
	refresh_button.pressed.connect(_on_refresh_pressed)
	player_name_edit.text_changed.connect(_on_player_name_changed)
	
	# 连接网络信号
	NetworkManager.room_discovered.connect(_on_room_discovered)
	NetworkManager.room_list_updated.connect(_on_room_list_updated)
	NetworkManager.connection_succeeded.connect(_on_connection_succeeded)
	NetworkManager.connection_failed.connect(_on_connection_failed)
	
	# 初始化UI
	setup_ui()
	
	# 开始搜索房间
	start_room_discovery()

func setup_ui():
	# 设置玩家名称
	player_name_edit.text = GameData.player_name
	
	# 设置默认房间名称
	room_name_edit.text = GameData.player_name + "的房间"
	
	# 设置最大玩家数
	max_players_spin.min_value = 2
	max_players_spin.max_value = 8
	max_players_spin.value = 4
	max_players_spin.step = 1
	
	# 更新状态
	update_status("搜索房间中...")

func start_room_discovery():
	NetworkManager.start_room_discovery()
	update_status("搜索房间中...")

func update_status(text: String):
	status_label.text = text

func create_room_button(room_info) -> Control:
	var room_container = HBoxContainer.new()
	room_container.custom_minimum_size = Vector2(0, 60)
	
	# 房间信息标签
	var info_label = Label.new()
	info_label.text = "%s (%s) - %d/%d 玩家 - %s" % [
		room_info.room_name,
		room_info.host_name,
		room_info.current_players,
		room_info.max_players,
		room_info.level_name
	]
	info_label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	info_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	
	# 加入按钮
	var join_button = Button.new()
	join_button.text = "加入"
	join_button.custom_minimum_size = Vector2(80, 0)
	
	# 检查房间是否已满
	if room_info.current_players >= room_info.max_players:
		join_button.disabled = true
		join_button.text = "已满"
	else:
		join_button.pressed.connect(func(): join_room(room_info))
	
	room_container.add_child(info_label)
	room_container.add_child(join_button)
	
	# 添加分隔线
	var separator = HSeparator.new()
	
	var container = VBoxContainer.new()
	container.add_child(room_container)
	container.add_child(separator)
	
	return container

func join_room(room_info):
	update_status("正在连接到房间...")
	create_room_button.disabled = true
	refresh_button.disabled = true
	
	if NetworkManager.join_room(room_info):
		# 连接成功会通过信号处理
		pass
	else:
		update_status("连接失败")
		create_room_button.disabled = false
		refresh_button.disabled = false

func _on_back_pressed():
	# 断开网络连接
	NetworkManager.disconnect_from_room()

	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func _on_create_room_pressed():
	var room_name = room_name_edit.text.strip_edges()
	if room_name.is_empty():
		update_status("请输入房间名称")
		return
	
	if not GameData.selected_level:
		update_status("请先选择关卡")
		get_tree().change_scene_to_file("res://scenes/LevelSelect.tscn")
		return
	
	update_status("正在创建房间...")
	create_room_button.disabled = true
	
	var max_players = int(max_players_spin.value)
	if NetworkManager.create_room(room_name, max_players):
		# 创建成功，跳转到房间大厅
		get_tree().change_scene_to_file("res://scenes/RoomLobby.tscn")
	else:
		update_status("创建房间失败")
		create_room_button.disabled = false

func _on_refresh_pressed():
	# 清空房间列表
	for child in room_list.get_children():
		child.queue_free()
	
	# 重新开始搜索
	NetworkManager.stop_room_discovery()
	start_room_discovery()

func _on_player_name_changed(new_name: String):
	GameData.player_name = new_name.strip_edges()
	if GameData.player_name.is_empty():
		GameData.player_name = "Player"
	
	# 更新默认房间名称
	room_name_edit.text = GameData.player_name + "的房间"

func _on_room_discovered(room_info):
	print("Room discovered: ", room_info.room_name)

func _on_room_list_updated(rooms: Array):
	# 清空现有列表
	for child in room_list.get_children():
		child.queue_free()
	
	if rooms.is_empty():
		var no_rooms_label = Label.new()
		no_rooms_label.text = "未发现任何房间"
		no_rooms_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		room_list.add_child(no_rooms_label)
	else:
		# 添加房间按钮
		for room_info in rooms:
			var room_button = create_room_button(room_info)
			room_list.add_child(room_button)
	
	update_status("发现 %d 个房间" % rooms.size())

func _on_connection_succeeded():
	update_status("连接成功！")
	# 跳转到房间大厅
	get_tree().change_scene_to_file("res://scenes/RoomLobby.tscn")

func _on_connection_failed(error: String):
	update_status("连接失败: " + error)
	create_room_button.disabled = false
	refresh_button.disabled = false

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
