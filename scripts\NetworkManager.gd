extends Node

# 房间信息结构
class RoomInfo:
	var room_name: String
	var host_name: String
	var current_players: int
	var max_players: int
	var level_name: String
	var ip_address: String
	var port: int
	
	func _init(p_room_name: String, p_host_name: String, p_current: int, p_max: int, p_level: String, p_ip: String, p_port: int):
		room_name = p_room_name
		host_name = p_host_name
		current_players = p_current
		max_players = p_max
		level_name = p_level
		ip_address = p_ip
		port = p_port

# 信号
signal room_discovered(room_info: RoomInfo)
signal room_list_updated(rooms: Array)
signal connection_succeeded()
signal connection_failed(error: String)
signal player_joined(player_name: String)
signal player_left(player_name: String)
signal host_disconnected()

# 网络配置
const DEFAULT_PORT = 7000
const BROADCAST_PORT = 7001
const MAX_PLAYERS = 8

# 网络状态
var is_hosting: bool = false
var is_connected: bool = false
var current_room: RoomInfo
var discovered_rooms: Array = []

# UDP广播相关
var udp_server: UDPServer
var udp_client: PacketPeerUDP
var broadcast_timer: Timer

# 玩家数据
var connected_players: Dictionary = {}  # peer_id -> player_name

func _ready():
	# 设置多人游戏信号
	multiplayer.peer_connected.connect(_on_peer_connected)
	multiplayer.peer_disconnected.connect(_on_peer_disconnected)
	multiplayer.connected_to_server.connect(_on_connected_to_server)
	multiplayer.connection_failed.connect(_on_connection_failed)
	multiplayer.server_disconnected.connect(_on_server_disconnected)
	
	# 创建广播定时器
	broadcast_timer = Timer.new()
	broadcast_timer.wait_time = 2.0
	broadcast_timer.timeout.connect(_broadcast_room_info)
	add_child(broadcast_timer)

# 创建房间（作为主机）
func create_room(room_name: String, max_players: int = 4) -> bool:
	var peer = ENetMultiplayerPeer.new()
	var error = peer.create_server(DEFAULT_PORT, max_players)
	
	if error != OK:
		print("Failed to create server: ", error)
		return false
	
	multiplayer.multiplayer_peer = peer
	is_hosting = true
	is_connected = true
	
	# 创建房间信息
	current_room = RoomInfo.new(
		room_name,
		GameData.player_name,
		1,
		max_players,
		GameData.selected_level.name if GameData.selected_level else "未选择",
		get_local_ip(),
		DEFAULT_PORT
	)
	
	# 添加自己到玩家列表
	connected_players[1] = GameData.player_name  # 主机ID总是1
	
	# 开始广播房间信息
	start_broadcasting()
	
	print("Room created: ", room_name)
	return true

# 加入房间
func join_room(room_info: RoomInfo) -> bool:
	var peer = ENetMultiplayerPeer.new()
	var error = peer.create_client(room_info.ip_address, room_info.port)
	
	if error != OK:
		print("Failed to create client: ", error)
		connection_failed.emit("无法连接到服务器")
		return false
	
	multiplayer.multiplayer_peer = peer
	current_room = room_info
	
	print("Attempting to join room: ", room_info.room_name)
	return true

# 开始搜索房间
func start_room_discovery():
	stop_room_discovery()
	
	udp_client = PacketPeerUDP.new()
	udp_client.bind(BROADCAST_PORT)
	
	# 定期检查广播消息
	var discovery_timer = Timer.new()
	discovery_timer.wait_time = 0.1
	discovery_timer.timeout.connect(_check_for_broadcasts)
	add_child(discovery_timer)
	discovery_timer.start()
	
	print("Started room discovery")

# 停止搜索房间
func stop_room_discovery():
	if udp_client:
		udp_client.close()
		udp_client = null
	
	discovered_rooms.clear()

# 开始广播房间信息
func start_broadcasting():
	if not is_hosting:
		return
	
	udp_server = UDPServer.new()
	udp_server.listen(BROADCAST_PORT)
	broadcast_timer.start()

# 停止广播
func stop_broadcasting():
	if udp_server:
		udp_server.stop()
		udp_server = null
	broadcast_timer.stop()

# 断开连接
func disconnect_from_room():
	if multiplayer.multiplayer_peer:
		multiplayer.multiplayer_peer.close()
		multiplayer.multiplayer_peer = null
	
	is_hosting = false
	is_connected = false
	current_room = null
	connected_players.clear()
	
	stop_broadcasting()
	stop_room_discovery()
	
	print("Disconnected from room")

# 获取本地IP地址
func get_local_ip() -> String:
	var addresses = IP.get_local_addresses()
	for address in addresses:
		if address.begins_with("192.168.") or address.begins_with("10.") or address.begins_with("172."):
			return address
	return "127.0.0.1"

# 广播房间信息
func _broadcast_room_info():
	if not is_hosting or not current_room:
		return
	
	var room_data = {
		"room_name": current_room.room_name,
		"host_name": current_room.host_name,
		"current_players": connected_players.size(),
		"max_players": current_room.max_players,
		"level_name": current_room.level_name,
		"ip_address": current_room.ip_address,
		"port": current_room.port
	}
	
	var json_string = JSON.stringify(room_data)
	var packet = json_string.to_utf8_buffer()
	
	# 广播到局域网
	udp_server.put_packet(packet, "***************", BROADCAST_PORT)

# 检查广播消息
func _check_for_broadcasts():
	if not udp_client:
		return
	
	while udp_client.get_available_packet_count() > 0:
		var packet = udp_client.get_packet()
		var json_string = packet.get_string_from_utf8()
		
		var json = JSON.new()
		var parse_result = json.parse(json_string)
		
		if parse_result == OK:
			var room_data = json.data
			var room_info = RoomInfo.new(
				room_data.room_name,
				room_data.host_name,
				room_data.current_players,
				room_data.max_players,
				room_data.level_name,
				room_data.ip_address,
				room_data.port
			)
			
			# 检查是否是新房间
			var found = false
			for existing_room in discovered_rooms:
				if existing_room.ip_address == room_info.ip_address and existing_room.port == room_info.port:
					# 更新现有房间信息
					existing_room.current_players = room_info.current_players
					existing_room.level_name = room_info.level_name
					found = true
					break
			
			if not found:
				discovered_rooms.append(room_info)
				room_discovered.emit(room_info)
			
			room_list_updated.emit(discovered_rooms)

# 网络事件处理
func _on_peer_connected(peer_id: int):
	print("Player connected: ", peer_id)
	# 主机向新玩家发送当前玩家列表
	if is_hosting:
		rpc_id(peer_id, "_receive_player_list", connected_players)

func _on_peer_disconnected(peer_id: int):
	if peer_id in connected_players:
		var player_name = connected_players[peer_id]
		connected_players.erase(peer_id)
		player_left.emit(player_name)
		print("Player disconnected: ", player_name)

func _on_connected_to_server():
	is_connected = true
	connection_succeeded.emit()
	# 向服务器发送玩家信息
	rpc("_register_player", GameData.player_name)

func _on_connection_failed():
	connection_failed.emit("连接失败")

func _on_server_disconnected():
	host_disconnected.emit()
	disconnect_from_room()

# RPC函数
@rpc("any_peer", "reliable")
func _register_player(player_name: String):
	var peer_id = multiplayer.get_remote_sender_id()
	connected_players[peer_id] = player_name
	player_joined.emit(player_name)
	
	# 广播给所有玩家
	rpc("_receive_player_list", connected_players)

@rpc("authority", "reliable")
func _receive_player_list(players: Dictionary):
	connected_players = players
	# 发送信号更新UI
	for player_name in players.values():
		if player_name != GameData.player_name:
			player_joined.emit(player_name)
