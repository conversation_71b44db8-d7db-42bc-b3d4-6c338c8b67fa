extends CharacterBody2D
class_name Player

# 玩家移动参数
@export var speed: float = 200.0
@export var jump_velocity: float = -533.0
@export var acceleration: float = 1000.0
@export var friction: float = 1000.0
@export var air_resistance: float = 200.0

# 跳跃相关
@export var coyote_time: float = 0.1
@export var jump_buffer_time: float = 0.1

# 攻击相关
@export var attack_duration: float = 0.3
@export var attack_cooldown: float = 0.5

# 内部变量
var coyote_timer: float = 0.0
var jump_buffer_timer: float = 0.0
var was_on_floor: bool = false
var is_attacking: bool = false
var attack_timer: float = 0.0
var attack_cooldown_timer: float = 0.0

# 节点引用
@onready var sprite: Sprite2D = $Sprite2D
@onready var animation_player: AnimationPlayer = $AnimationPlayer
@onready var collision_shape: CollisionShape2D = $CollisionShape2D

# 获取重力值
var gravity = ProjectSettings.get_setting("physics/2d/default_gravity")

func _ready():
	# 初始化玩家
	pass

func _physics_process(delta):
	# 只有拥有权限的玩家才能控制
	if not is_multiplayer_authority():
		return

	handle_gravity(delta)
	handle_jump(delta)
	handle_attack(delta)
	handle_movement(delta)
	handle_animations()

	# 移动角色
	move_and_slide()

	# 更新地面状态
	update_floor_state()

	# 同步位置和状态到其他玩家
	if GameData.is_multiplayer():
		rpc_unreliable("_sync_player_state", global_position, velocity, is_attacking)

func handle_gravity(delta):
	# 添加重力
	if not is_on_floor():
		velocity.y += gravity * delta

func handle_jump(delta):
	# 更新计时器
	if coyote_timer > 0:
		coyote_timer -= delta
	if jump_buffer_timer > 0:
		jump_buffer_timer -= delta

	# 检测跳跃输入
	if Input.is_action_just_pressed("jump") and not is_attacking:
		jump_buffer_timer = jump_buffer_time

	# 执行跳跃
	if jump_buffer_timer > 0 and (is_on_floor() or coyote_timer > 0):
		velocity.y = jump_velocity
		jump_buffer_timer = 0
		coyote_timer = 0

func handle_attack(delta):
	# 更新攻击计时器
	if attack_timer > 0:
		attack_timer -= delta
		if attack_timer <= 0:
			is_attacking = false

	if attack_cooldown_timer > 0:
		attack_cooldown_timer -= delta

	# 检测攻击输入
	if Input.is_action_just_pressed("attack") and not is_attacking and attack_cooldown_timer <= 0:
		start_attack()

func start_attack():
	is_attacking = true
	attack_timer = attack_duration
	attack_cooldown_timer = attack_cooldown
	print("Player attacks!")

func handle_movement(delta):
	# 获取输入方向
	var direction = Input.get_axis("move_left", "move_right")

	# 攻击时移动速度减半
	var current_speed = speed
	if is_attacking:
		current_speed *= 0.5

	if direction != 0:
		# 加速
		if is_on_floor():
			velocity.x = move_toward(velocity.x, direction * current_speed, acceleration * delta)
		else:
			velocity.x = move_toward(velocity.x, direction * current_speed, air_resistance * delta)

		# 翻转精灵（攻击时不翻转）
		if not is_attacking:
			if direction > 0:
				sprite.flip_h = false
			elif direction < 0:
				sprite.flip_h = true
	else:
		# 减速
		if is_on_floor():
			velocity.x = move_toward(velocity.x, 0, friction * delta)
		else:
			velocity.x = move_toward(velocity.x, 0, air_resistance * delta)

func handle_animations():
	# 处理动画
	if is_attacking:
		play_animation("attack")
	elif not is_on_floor():
		if velocity.y < 0:
			play_animation("jump")
		else:
			play_animation("fall")
	elif abs(velocity.x) > 10:
		play_animation("run")
	else:
		play_animation("idle")

func play_animation(anim_name: String):
	if animation_player.has_animation(anim_name):
		if animation_player.current_animation != anim_name:
			animation_player.play(anim_name)

func update_floor_state():
	# 更新土狼时间
	if was_on_floor and not is_on_floor():
		coyote_timer = coyote_time
	
	was_on_floor = is_on_floor()

# 重置玩家位置（用于重生）
func reset_position(pos: Vector2):
	global_position = pos
	velocity = Vector2.ZERO

# 玩家死亡处理
func die():
	# 这里可以添加死亡动画和效果
	print("Player died!")
	# 可以发送信号给游戏管理器

# 网络同步函数
@rpc("any_peer", "unreliable")
func _sync_player_state(pos: Vector2, vel: Vector2, attacking: bool):
	if is_multiplayer_authority():
		return  # 不同步自己的状态

	# 更新其他玩家的位置和状态
	global_position = pos
	velocity = vel
	is_attacking = attacking

	# 更新动画
	handle_animations()
