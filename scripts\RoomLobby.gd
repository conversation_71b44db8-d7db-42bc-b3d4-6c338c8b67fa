extends Control
class_name RoomLobby

# 节点引用
@onready var back_button: Button = $VBoxContainer/TopBar/BackButton
@onready var room_name_label: Label = $VBoxContainer/TopBar/RoomNameLabel
@onready var level_name_label: Label = $VBoxContainer/LevelInfo/LevelNameLabel
@onready var change_level_button: Button = $VBoxContainer/LevelInfo/ChangeLevelButton
@onready var player_list: VBoxContainer = $VBoxContainer/PlayerSection/ScrollContainer/PlayerList
@onready var ready_button: Button = $VBoxContainer/BottomBar/ReadyButton
@onready var start_game_button: Button = $VBoxContainer/BottomBar/StartGameButton
@onready var status_label: Label = $VBoxContainer/StatusLabel

# 玩家状态
var player_ready_states: Dictionary = {}  # peer_id -> bool
var is_ready: bool = false

func _ready():
	
	# 连接信号
	back_button.pressed.connect(_on_back_pressed)
	change_level_button.pressed.connect(_on_change_level_pressed)
	ready_button.pressed.connect(_on_ready_pressed)
	start_game_button.pressed.connect(_on_start_game_pressed)
	
	# 连接网络信号
	NetworkManager.player_joined.connect(_on_player_joined)
	NetworkManager.player_left.connect(_on_player_left)
	NetworkManager.host_disconnected.connect(_on_host_disconnected)

	# 设置UI
	setup_ui()

	# 如果是主机，显示开始游戏按钮
	start_game_button.visible = NetworkManager.is_hosting
	change_level_button.visible = NetworkManager.is_hosting

func setup_ui():
	# 设置房间名称
	if NetworkManager.current_room:
		room_name_label.text = NetworkManager.current_room.room_name
	
	# 设置关卡信息
	if GameData.selected_level:
		level_name_label.text = "当前关卡: " + GameData.selected_level.name
	else:
		level_name_label.text = "当前关卡: 未选择"
	
	# 更新玩家列表
	update_player_list()
	
	# 设置准备按钮
	ready_button.text = "准备"
	is_ready = false
	
	update_status("等待玩家准备...")

func update_player_list():
	# 清空现有列表
	for child in player_list.get_children():
		child.queue_free()
	
	# 添加玩家
	for peer_id in NetworkManager.connected_players:
		var player_name = NetworkManager.connected_players[peer_id]
		var player_item = create_player_item(player_name, peer_id)
		player_list.add_child(player_item)

func create_player_item(player_name: String, peer_id: int) -> Control:
	var container = HBoxContainer.new()
	container.custom_minimum_size = Vector2(0, 40)
	
	# 玩家名称
	var name_label = Label.new()
	name_label.text = player_name
	name_label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	name_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	
	# 主机标识
	if peer_id == 1:  # 主机ID总是1
		name_label.text += " (主机)"
	
	# 准备状态
	var ready_label = Label.new()
	ready_label.custom_minimum_size = Vector2(80, 0)
	ready_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	ready_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	
	if peer_id in player_ready_states and player_ready_states[peer_id]:
		ready_label.text = "已准备"
		ready_label.modulate = Color.GREEN
	else:
		ready_label.text = "未准备"
		ready_label.modulate = Color.RED
	
	container.add_child(name_label)
	container.add_child(ready_label)
	
	return container

func update_status(text: String):
	status_label.text = text

func check_all_ready() -> bool:
	# 检查所有玩家是否都已准备
	for peer_id in NetworkManager.connected_players:
		if peer_id == 1:  # 主机不需要准备
			continue
		if not (peer_id in player_ready_states and player_ready_states[peer_id]):
			return false
	return true

func _on_back_pressed():
	# 断开连接并返回
	NetworkManager.disconnect_from_room()
	get_tree().change_scene_to_file("res://scenes/MultiplayerLobby.tscn")

func _on_change_level_pressed():
	if not NetworkManager.is_hosting:
		return

	# 主机可以更改关卡
	get_tree().change_scene_to_file("res://scenes/LevelSelect.tscn")

func _on_ready_pressed():
	if NetworkManager.is_hosting:
		return  # 主机不需要准备
	
	is_ready = !is_ready
	
	if is_ready:
		ready_button.text = "取消准备"
		ready_button.modulate = Color.GREEN
	else:
		ready_button.text = "准备"
		ready_button.modulate = Color.WHITE
	
	# 发送准备状态给主机
	rpc("_update_ready_state", multiplayer.get_unique_id(), is_ready)

func _on_start_game_pressed():
	if not NetworkManager.is_hosting:
		return
	
	# 检查是否选择了关卡
	if not GameData.selected_level:
		update_status("请先选择关卡")
		return
	
	# 检查是否所有玩家都已准备
	if not check_all_ready():
		update_status("等待所有玩家准备")
		return
	
	# 开始游戏
	rpc("_start_game", GameData.selected_level.scene_path)

func _on_player_joined(player_name: String):
	update_player_list()
	update_status(player_name + " 加入了房间")

func _on_player_left(player_name: String):
	update_player_list()
	update_status(player_name + " 离开了房间")

func _on_host_disconnected():
	update_status("主机已断开连接")
	# 返回到多人大厅
	await get_tree().create_timer(2.0).timeout
	get_tree().change_scene_to_file("res://scenes/MultiplayerLobby.tscn")

# RPC函数
@rpc("any_peer", "reliable")
func _update_ready_state(peer_id: int, ready: bool):
	player_ready_states[peer_id] = ready
	update_player_list()
	
	if NetworkManager.is_hosting:
		# 主机广播给所有玩家
		rpc("_receive_ready_state", peer_id, ready)
		
		# 检查是否可以开始游戏
		if check_all_ready():
			update_status("所有玩家已准备，可以开始游戏")
		else:
			update_status("等待玩家准备...")

@rpc("authority", "reliable")
func _receive_ready_state(peer_id: int, ready: bool):
	player_ready_states[peer_id] = ready
	update_player_list()

@rpc("authority", "reliable")
func _start_game(level_path: String):
	update_status("游戏开始！")
	get_tree().change_scene_to_file(level_path)

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
